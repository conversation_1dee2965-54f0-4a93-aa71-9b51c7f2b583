"""change_price_range_columns_to_decimal

Revision ID: a1b2c3d4e5f6
Revises: 6b3f21e5126d
Create Date: 2025-06-30 23:15:00.000000

This migration changes the price_range_min and price_range_max columns 
from Integer to DECIMAL(10, 2) for consistency with Product.price.
This ensures proper precision for price comparisons and filtering.

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a1b2c3d4e5f6'
down_revision: Union[str, None] = '6b3f21e5126d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Change price_range_min and price_range_max columns to DECIMAL(10, 2)."""
    print("🔧 Changing price_range_min and price_range_max columns to DECIMAL(10, 2)...")
    
    # Change price_range_min from Integer to DECIMAL(10, 2)
    op.execute("""
        ALTER TABLE end_users 
        ALTER COLUMN price_range_min TYPE NUMERIC(10,2) 
        USING price_range_min::numeric(10,2)
    """)
    
    # Change price_range_max from Integer to DECIMAL(10, 2)
    op.execute("""
        ALTER TABLE end_users 
        ALTER COLUMN price_range_max TYPE NUMERIC(10,2) 
        USING price_range_max::numeric(10,2)
    """)
    
    print("✅ Successfully changed price range columns to DECIMAL(10, 2)")


def downgrade() -> None:
    """Revert price_range_min and price_range_max columns back to Integer."""
    print("🔄 Reverting price_range_min and price_range_max columns to Integer...")
    
    # Revert price_range_max from DECIMAL(10, 2) to Integer
    op.execute("""
        ALTER TABLE end_users 
        ALTER COLUMN price_range_max TYPE INTEGER 
        USING price_range_max::integer
    """)
    
    # Revert price_range_min from DECIMAL(10, 2) to Integer
    op.execute("""
        ALTER TABLE end_users 
        ALTER COLUMN price_range_min TYPE INTEGER 
        USING price_range_min::integer
    """)
    
    print("✅ Successfully reverted price range columns to Integer")
