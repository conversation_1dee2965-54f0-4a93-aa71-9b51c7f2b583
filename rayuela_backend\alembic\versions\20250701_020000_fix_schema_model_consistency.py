"""Fix schema to match SQLAlchemy models

Revision ID: 20250701_020000
Revises: 20250701_010001
Create Date: 2025-07-01 02:00:00.000000

This migration fixes the database schema to match the SQLAlchemy models exactly.
Since there's no data yet, we can safely restructure the tables.

Key changes:
1. EndUser: Change from end_user_id (single PK) to user_id + account_id (composite PK)
2. Interaction: Change from interaction_id (single PK) to account_id + id (composite PK), end_user_id -> user_id
3. Search: Change from search_id (single PK) to account_id + id (composite PK), end_user_id -> user_id  
4. AuditLog: Change from audit_id (single PK) to account_id + id (composite PK)
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250701_020000"
down_revision: Union[str, None] = "20250701_010001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _drop_constraint_if_exists(constraint_name: str, table_name: str, constraint_type: str):
    """Helper function to drop constraint only if it exists"""
    connection = op.get_bind()

    # Check if constraint exists
    result = connection.execute(text("""
        SELECT constraint_name
        FROM information_schema.table_constraints
        WHERE table_name = :table_name
        AND constraint_name = :constraint_name
        AND table_schema = 'public'
    """), {"table_name": table_name, "constraint_name": constraint_name})

    if result.fetchone():
        print(f"  ✅ Dropping existing constraint: {constraint_name}")
        op.drop_constraint(constraint_name, table_name, type_=constraint_type)
    else:
        print(f"  ⚠️ Constraint {constraint_name} does not exist, skipping...")


def _column_exists(table_name: str, column_name: str) -> bool:
    """Helper function to check if a column exists"""
    connection = op.get_bind()

    result = connection.execute(text("""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = :table_name
        AND column_name = :column_name
        AND table_schema = 'public'
    """), {"table_name": table_name, "column_name": column_name})

    return result.fetchone() is not None


def _alter_column_if_exists(table_name: str, old_column_name: str, new_column_name: str):
    """Helper function to rename column only if it exists"""
    if _column_exists(table_name, old_column_name):
        print(f"  ✅ Renaming column {old_column_name} to {new_column_name}")
        op.alter_column(table_name, old_column_name, new_column_name=new_column_name)
    else:
        print(f"  ⚠️ Column {old_column_name} does not exist, skipping...")


def _drop_column_if_exists(table_name: str, column_name: str):
    """Helper function to drop column only if it exists"""
    if _column_exists(table_name, column_name):
        print(f"  ✅ Dropping column {column_name}")
        op.drop_column(table_name, column_name)
    else:
        print(f"  ⚠️ Column {column_name} does not exist, skipping...")


def upgrade() -> None:
    """Apply schema fixes to match SQLAlchemy models."""
    
    print("Fixing database schema to match SQLAlchemy models...")
    
    # 1. Fix EndUser table
    print("1. Fixing EndUser table structure...")

    # Drop existing constraints and indexes
    _drop_constraint_if_exists('uq_end_user_account_external_id', 'end_users', 'unique')
    _drop_constraint_if_exists('end_users_pkey', 'end_users', 'primary')
    
    # Rename end_user_id to user_id
    _alter_column_if_exists('end_users', 'end_user_id', 'user_id')
    
    # Create composite primary key (user_id, account_id)
    op.create_primary_key('end_users_pkey', 'end_users', ['user_id', 'account_id'])
    
    # Recreate unique constraint with correct name
    op.create_unique_constraint('uq_end_user_external_id', 'end_users', ['account_id', 'external_id'])
    
    
    # 2. Fix Interaction table
    print("2. Fixing Interaction table structure...")

    # Drop all foreign key constraints first
    _drop_constraint_if_exists('interactions_end_user_id_fkey', 'interactions', 'foreignkey')
    _drop_constraint_if_exists('interactions_product_id_fkey', 'interactions', 'foreignkey')
    _drop_constraint_if_exists('interactions_pkey', 'interactions', 'primary')
    
    # Rename columns to match model
    _alter_column_if_exists('interactions', 'interaction_id', 'id')
    _alter_column_if_exists('interactions', 'end_user_id', 'user_id')
    _alter_column_if_exists('interactions', 'rating', 'value')
    _alter_column_if_exists('interactions', 'context', 'recommendation_metadata')

    # Remove extra columns that don't exist in model
    _drop_column_if_exists('interactions', 'session_id')
    _drop_column_if_exists('interactions', 'created_at')
    _drop_column_if_exists('interactions', 'updated_at')
    
    # Create composite primary key (account_id, id)
    op.create_primary_key('interactions_pkey', 'interactions', ['account_id', 'id'])
    
    # Recreate foreign key constraints with composite keys
    op.create_foreign_key(
        'fk_interaction_user', 'interactions', 'end_users',
        ['account_id', 'user_id'], ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    op.create_foreign_key(
        'fk_interaction_product', 'interactions', 'products', 
        ['account_id', 'product_id'], ['account_id', 'product_id'],
        ondelete='CASCADE'
    )
    
    
    # 3. Fix Search table
    print("3. Fixing Search table structure...")

    # Drop constraints
    _drop_constraint_if_exists('searches_end_user_id_fkey', 'searches', 'foreignkey')
    _drop_constraint_if_exists('searches_pkey', 'searches', 'primary')
    
    # Rename columns
    _alter_column_if_exists('searches', 'search_id', 'id')
    _alter_column_if_exists('searches', 'end_user_id', 'user_id')

    # Remove extra columns that don't exist in model
    _drop_column_if_exists('searches', 'filters')
    _drop_column_if_exists('searches', 'results_count')
    _drop_column_if_exists('searches', 'session_id')
    _drop_column_if_exists('searches', 'created_at')
    _drop_column_if_exists('searches', 'updated_at')
    
    # Create composite primary key (account_id, id)
    op.create_primary_key('searches_pkey', 'searches', ['account_id', 'id'])
    
    # Recreate foreign key constraint with composite key
    op.create_foreign_key(
        'fk_search_user', 'searches', 'end_users',
        ['account_id', 'user_id'], ['account_id', 'user_id'],
        ondelete='CASCADE'
    )


    # 4. Fix AuditLog table
    print("4. Fixing AuditLog table structure...")

    # Drop constraints
    _drop_constraint_if_exists('audit_logs_user_id_fkey', 'audit_logs', 'foreignkey')
    _drop_constraint_if_exists('audit_logs_pkey', 'audit_logs', 'primary')

    # Rename columns to match model
    _alter_column_if_exists('audit_logs', 'audit_id', 'id')
    _alter_column_if_exists('audit_logs', 'old_values', 'changes')
    _alter_column_if_exists('audit_logs', 'timestamp', 'created_at')

    # Remove columns that don't exist in the model
    _drop_column_if_exists('audit_logs', 'user_id')
    _drop_column_if_exists('audit_logs', 'new_values')
    _drop_column_if_exists('audit_logs', 'ip_address')
    _drop_column_if_exists('audit_logs', 'user_agent')

    # Create composite primary key (account_id, id)
    op.create_primary_key('audit_logs_pkey', 'audit_logs', ['account_id', 'id'])

    print("Schema consistency fixes completed successfully!")


def downgrade() -> None:
    """Revert schema fixes."""
    
    # This is a complex downgrade that would need to reverse all the changes
    # For now, we'll raise an exception to prevent accidental downgrades
    raise NotImplementedError(
        "Downgrade not implemented for schema consistency fixes. "
        "This migration makes structural changes that are difficult to reverse safely."
    )
