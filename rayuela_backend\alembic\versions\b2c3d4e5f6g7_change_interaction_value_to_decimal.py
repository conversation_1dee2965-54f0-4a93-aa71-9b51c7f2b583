"""change_interaction_value_to_decimal

Revision ID: b2c3d4e5f6g7
Revises: a1b2c3d4e5f6
Create Date: 2025-06-30 23:30:00.000000

This migration changes the Interaction.value column from Float to DECIMAL(5, 2)
for precise rating calculations and to avoid floating-point precision issues
in aggregations and comparisons.

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b2c3d4e5f6g7'
down_revision: Union[str, None] = 'a1b2c3d4e5f6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Change Interaction.value column from Float to DECIMAL(5, 2)."""
    print("🔧 Changing Interaction.value column from Float to DECIMAL(5, 2)...")
    
    # Change value from Float to DECIMAL(5, 2)
    op.execute("""
        ALTER TABLE interactions 
        ALTER COLUMN value TYPE NUMERIC(5,2) 
        USING value::numeric(5,2)
    """)
    
    print("✅ Successfully changed Interaction.value to DECIMAL(5, 2)")


def downgrade() -> None:
    """Revert Interaction.value column back to Float."""
    print("🔄 Reverting Interaction.value column to Float...")
    
    # Revert value from DECIMAL(5, 2) to Float
    op.execute("""
        ALTER TABLE interactions 
        ALTER COLUMN value TYPE REAL 
        USING value::real
    """)
    
    print("✅ Successfully reverted Interaction.value to Float")
