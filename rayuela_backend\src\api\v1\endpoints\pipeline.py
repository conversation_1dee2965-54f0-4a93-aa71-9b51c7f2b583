from fastapi import APIRouter, Depends, HTTPException, Request, Body, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from src.db import models, schemas, enums
from src.db.models.system_user import SystemUser
from src.db.models.interaction import Interaction
from src.services import LimitService
from src.db.repositories.subscription import SubscriptionRepository
from src.core.deps import get_current_admin_user, get_limit_service
from src.db.session import get_db
from src.core.deps import get_current_account
from src.ml_pipeline.training_pipeline import TrainingPipeline
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.ml_pipeline.evaluation import RecommendationEvaluator
from src.core.exceptions import ResourceNotFoundError, LimitExceededError
from src.utils.base_logger import log_info, log_error
from src.utils.security import verify_resource_ownership
from src.workers.celery_tasks import train_model, train_model_for_job
import asyncio
from src.services.cloud_run_launcher import launch_training_job
import os

router = APIRouter()
artifact_manager = ModelArtifactManager()
metrics_tracker = MetricsTracker()
evaluator = RecommendationEvaluator()
training_pipeline = TrainingPipeline(
    artifact_manager=artifact_manager,
    metrics_tracker=metrics_tracker,
    evaluator=evaluator
)

# Inicializar el training pipeline
@router.on_event("startup")
async def startup_event():
    # Inicializar al arrancar
    await training_pipeline.initialize() if hasattr(training_pipeline, "initialize") else None
    log_info("Training pipeline initialized on startup")


@router.post("/train", response_model=schemas.TrainingResponse, status_code=status.HTTP_202_ACCEPTED)
async def train_models(
    request: Request,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Inicia el entrenamiento de modelos usando Celery.

    Verifica los límites de API y la frecuencia de entrenamiento permitida según el plan de suscripción
    antes de crear el trabajo de entrenamiento y encolar la tarea.

    Returns:
        TrainingResponse: Respuesta con el ID del trabajo y la tarea iniciada.
        Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.
    """
    try:
        # Validar límites de API para entrenamiento de modelos
        await limit_service.validate_api_call_limit("model_training")

        # Validar frecuencia de entrenamiento según el plan de suscripción
        try:
            await limit_service.validate_training_frequency()
        except LimitExceededError as e:
            log_error(f"Training frequency limit exceeded for account {account.account_id}: {str(e)}")
            raise HTTPException(
                status_code=429,
                detail=str(e),
            )

        # Validar límites de datos de entrenamiento antes de encolar la tarea
        # Contar interacciones para validar límites de datos antes de encolar
        total_interactions_query = select(func.count(Interaction.id)).where(
            Interaction.account_id == account.account_id
        )
        total_interactions = (await db.execute(total_interactions_query)).scalar_one()

        try:
            await limit_service.validate_training_data_limit(total_interactions)
        except LimitExceededError as e:
            log_error(f"Training data limit exceeded for account {account.account_id}: {str(e)}")
            raise HTTPException(status_code=429, detail=str(e))

        # Obtener datos de entrenamiento
        data = await request.json()

        # Crear un registro de trabajo de entrenamiento y actualizar con el ID de la tarea
        # en una sola transacción
        async with db.begin():
            # Crear el trabajo de entrenamiento con los campos correctos
            training_job = models.TrainingJob()
            training_job.account_id = account.account_id
            training_job.status = enums.TrainingJobStatus.PENDING
            training_job.created_at = datetime.now(timezone.utc)
            training_job.parameters = data
            db.add(training_job)
            await db.flush()  # Para obtener el ID generado

            # Determinar estrategia de ejecución (Cloud Run Job vs Celery)
            use_cloud_run_jobs = os.getenv("USE_CLOUD_RUN_JOBS", "false").lower() == "true"

            if use_cloud_run_jobs:
                # Lanzar un Cloud Run Job de entrenamiento (bloqueante en hilo aparte)
                job_name = await asyncio.to_thread(
                    launch_training_job,
                    account.account_id,
                    training_job.id,
                    data,
                )
                training_job.task_id = job_name  # Reutilizamos task_id para almacenar el nombre del Job
                training_job.status = enums.TrainingJobStatus.RUNNING
            else:
                # Iniciar entrenamiento con Celery (modo legacy / local)
                task = train_model.apply_async(
                    args=[account.account_id, data],
                    queue="training"
                )
                training_job.task_id = task.id

        # Refrescar el objeto después de la transacción
        await db.refresh(training_job)

        task_identifier = training_job.task_id
        log_info(f"Training job {training_job.id} started for account {account.account_id} with identifier {task_identifier}")

        return {
            "message": "Training started successfully",
            "job_id": training_job.id,
            "task_id": task_identifier,
        }

    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error starting training: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=schemas.TrainingStatus)
async def get_training_status(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Obtiene el estado del último entrenamiento completado.

    Este endpoint devuelve las métricas del último modelo entrenado exitosamente,
    no el estado de un trabajo en curso. Para consultar el estado de un trabajo
    específico, use el endpoint /pipeline/jobs/{job_id}/status.
    """
    try:
        metrics = await training_pipeline.get_metrics(
            db=db, account_id=account.account_id
        )
        return metrics

    except Exception as e:
        log_error(f"Error getting training status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}/status", response_model=schemas.TrainingJobStatus)
async def get_training_job_status(
    job_id: int,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Consulta el estado de un trabajo de entrenamiento específico.

    Args:
        job_id: ID del trabajo de entrenamiento
        account: Cuenta actual
        db: Sesión de base de datos

    Returns:
        Estado detallado del trabajo de entrenamiento
    """
    try:
        # Buscar el trabajo de entrenamiento
        query = select(models.TrainingJob).where(
            models.TrainingJob.account_id == account.account_id,
            models.TrainingJob.id == job_id
        )
        result = await db.execute(query)
        training_job = result.scalar_one_or_none()

        if not training_job:
            raise HTTPException(
                status_code=404,
                detail=f"Training job {job_id} not found"
            )

        # Preparar la respuesta con el estado del trabajo
        response = {
            "job_id": training_job.id,
            "status": training_job.status,
            "created_at": training_job.created_at,
            "started_at": training_job.started_at,
            "completed_at": training_job.completed_at,
            "error_message": training_job.error_message,
            "task_id": training_job.task_id,
            "parameters": training_job.parameters,
            "metrics": training_job.metrics,
        }

        # Si el trabajo está completado y tiene un modelo asociado, incluir información del modelo
        if training_job.status == enums.TrainingJobStatus.COMPLETED and training_job.artifact_metadata_id:
            # Buscar el modelo asociado
            model_query = select(models.ModelMetadata).where(
                models.ModelMetadata.account_id == account.account_id,
                models.ModelMetadata.id == training_job.artifact_metadata_id
            )
            model_result = await db.execute(model_query)
            model = model_result.scalar_one_or_none()

            if model:
                response["model"] = {
                    "id": model.id,
                    "artifact_name": model.artifact_name,
                    "artifact_version": model.artifact_version,
                    "description": model.description,
                    "training_date": model.training_date,
                }

        return response

    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error getting training job status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models", response_model=list[schemas.ModelMetadataResponse])
async def list_models(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit: int = 10,
    offset: int = 0,
):
    """Lista todos los modelos entrenados para la cuenta."""
    try:
        # Obtener todos los modelos de la cuenta
        stmt = (
            select(models.ModelMetadata)
            .where(models.ModelMetadata.account_id == account.account_id)
            .order_by(models.ModelMetadata.training_date.desc())
            .offset(offset)
            .limit(limit)
        )
        result = await db.execute(stmt)
        models_list = result.scalars().all()

        # Convertir a esquema de respuesta
        result_list = []
        for model in models_list:
            # Crear objeto de respuesta directamente
            try:
                # Convertir los valores a tipos Python nativos
                model_id = int(model.id) if model.id is not None else 0
                artifact_name = (
                    str(model.artifact_name) if model.artifact_name is not None else ""
                )
                artifact_version = (
                    str(model.artifact_version)
                    if model.artifact_version is not None
                    else ""
                )
                description = (
                    str(model.description) if model.description is not None else None
                )

                # Manejar la fecha
                if model.training_date is not None:
                    if isinstance(model.training_date, datetime):
                        training_date = model.training_date
                    else:
                        # Si no es un datetime, intentar convertirlo
                        training_date = datetime.fromisoformat(str(model.training_date))
                else:
                    training_date = datetime.now(timezone.utc)

                # Manejar los diccionarios
                performance_metrics = {}
                if hasattr(model, "performance_metrics"):
                    try:
                        if isinstance(model.performance_metrics, dict):
                            performance_metrics = model.performance_metrics
                    except (AttributeError, TypeError):
                        pass

                parameters = {}
                if hasattr(model, "parameters"):
                    try:
                        if isinstance(model.parameters, dict):
                            parameters = model.parameters
                    except (AttributeError, TypeError):
                        pass

                # Crear la respuesta
                model_response = schemas.ModelMetadataResponse(
                    id=model_id,
                    artifact_name=artifact_name,
                    artifact_version=artifact_version,
                    description=description,
                    training_date=training_date,
                    performance_metrics=performance_metrics,
                    parameters=parameters,
                )
                result_list.append(model_response)
            except Exception as e:
                log_error(f"Error creating ModelMetadataResponse: {str(e)}")

        return result_list

    except Exception as e:
        log_error(f"Error listing models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models/{model_id}/metrics", response_model=schemas.TrainingStatus)
async def get_model_metrics(
    model_id: int,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """Obtiene las métricas de un modelo específico."""
    try:
        # Verificar que el modelo pertenezca a la cuenta
        await verify_resource_ownership(
            db=db,
            model=models.ModelMetadata,
            resource_id=model_id,
            account_id=account.account_id,
            error_class=ResourceNotFoundError
        )

        metrics = await training_pipeline.get_metrics(
            db=db, account_id=account.account_id, model_id=model_id
        )
        return metrics

    except ResourceNotFoundError as e:
        log_error(f"Model not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        log_error(f"Error getting model metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/invalidate-cache")
async def invalidate_cache(
    account: schemas.AccountResponse = Depends(get_current_account),
    model_type: Optional[str] = None,
    metric_type: Optional[str] = None,
):
    """Invalida la caché de modelos y métricas."""
    try:
        await training_pipeline.invalidate_cache(
            account_id=account.account_id,
            model_type=model_type,
            metric_type=metric_type,
        )
        return {"message": "Cache invalidated successfully"}

    except Exception as e:
        log_error(f"Error invalidating cache: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/train/{account_id}", status_code=status.HTTP_202_ACCEPTED)
async def train_artifact_for_account(
    account_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: SystemUser = Depends(get_current_admin_user),  # Validación de admin
    limit_service: LimitService = Depends(get_limit_service),
) -> Dict[str, Any]:
    """
    Inicia un entrenamiento específico para la cuenta `account_id`.
    Solo para administradores del sistema.

    Returns:
        Dict[str, Any]: Respuesta con el ID del trabajo y la tarea iniciada.
        Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.
    """
    # Validar límites de API para entrenamiento de modelos
    await limit_service.validate_api_call_limit("model_training")

    # Validar frecuencia de entrenamiento según el plan de suscripción
    # Nota: Para administradores, podemos hacer esta validación opcional o saltarla
    # En este caso, la mantenemos para asegurar que no se sobrecargue el sistema
    try:
        # Configuramos el LimitService para la cuenta específica
        limit_service.account_id = account_id
        await limit_service.validate_training_frequency()
    except LimitExceededError as e:
        log_error(f"Training frequency limit exceeded for account {account_id}: {str(e)}")
        # Para administradores, mostramos la advertencia pero permitimos continuar
        log_info(f"Admin override: Allowing training despite frequency limit for account {account_id}")

    # Nota: La validación del límite de datos de entrenamiento se realiza en el worker
    # ya que necesitamos contar las interacciones en la base de datos

    account = await db.get(models.Account, account_id)
    if not account:
        raise HTTPException(status_code=404, detail="Account not found")

    # Crear el job y actualizar con el ID de la tarea en una sola transacción
    async with db.begin():
        # Crear el trabajo de entrenamiento con los campos correctos
        training_job = models.TrainingJob()
        training_job.account_id = account_id
        training_job.status = "pending"
        training_job.created_at = datetime.now(timezone.utc)
        training_job.parameters = {}
        db.add(training_job)
        await db.flush()  # Para obtener el ID generado

        # Iniciar entrenamiento con Celery
        # Nota: El IDE muestra un error porque no puede verificar que train_model_for_job tenga un método apply_async
        # Esto es normal porque train_model_for_job es una tarea Celery y el IDE no puede inferir su tipo
        task = train_model_for_job.apply_async(
            args=[account_id, training_job.id],
            queue="training"
        )

        # Actualizar el registro con el ID de la tarea
        training_job.task_id = task.id

    # Refrescar el objeto después de la transacción
    await db.refresh(training_job)

    log_info(
        f"Training task {task.id} started for account {account_id}, job {training_job.id}"
    )

    return {
        "message": "Training started successfully",
        "job_id": training_job.id,
        "task_id": task.id,
        "account_id": account_id,
    }


@router.post("/process")
async def process_training_job(
    payload: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    Endpoint para procesar trabajos de entrenamiento manualmente.
    Este endpoint es para uso administrativo o debugging.
    """
    try:
        account_id = payload.get("account_id")
        job_id = payload.get("job_id")

        if not account_id or not job_id:
            raise HTTPException(status_code=400, detail="Missing account_id or job_id")

        # Verificar que el trabajo pertenezca a la cuenta
        try:
            await verify_resource_ownership(
                db=db,
                model=models.TrainingJob,
                resource_id=job_id,
                account_id=account_id,
                error_class=lambda resource_type, resource_id: HTTPException(
                    status_code=404, detail=f"Training job {resource_id} not found"
                )
            )
        except HTTPException as e:
            # Si el trabajo no existe, lanzar la excepción
            raise e
        except Exception:
            # Si el trabajo existe pero no pertenece a la cuenta, lanzar una excepción de acceso denegado
            raise HTTPException(
                status_code=403, detail="Training job does not belong to this account"
            )

        # Buscar el trabajo de entrenamiento
        training_job = await db.get(models.TrainingJob, job_id)

        # Iniciar entrenamiento con Celery
        # Nota: El IDE muestra un error porque no puede verificar que train_model_for_job tenga un método apply_async
        # Esto es normal porque train_model_for_job es una tarea Celery y el IDE no puede inferir su tipo
        task = train_model_for_job.apply_async(
            args=[account_id, job_id],
            queue="training"
        )

        # Actualizar el registro con el ID de la tarea en una transacción
        async with db.begin():
            # Actualizar los campos del trabajo de entrenamiento
            # Nota: El IDE muestra errores porque no puede verificar que estos campos sean asignables
            # Esto es normal porque SQLAlchemy utiliza descriptores especiales para las columnas
            training_job.task_id = task.id
            training_job.status = (
                "processing"  # En producción, usar enums.TrainingJobStatus.PROCESSING
            )

        log_info(
            f"Training task {task.id} started manually for account {account_id}, job {job_id}"
        )

        return {
            "message": "Training started successfully",
            "job_id": job_id,
            "task_id": task.id,
            "account_id": account_id,
        }
    except ResourceNotFoundError as e:
        log_error(f"Resource not found in process_training_job: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        log_error(f"Error in process_training_job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/callback/{job_id}")
async def training_callback(
    job_id: int, payload: Dict[str, Any] = Body(...), db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Endpoint de callback para notificaciones de finalización de entrenamiento.
    Este endpoint puede ser llamado por Celery o por sistemas externos.
    """
    try:
        async with db.begin():
            account_id = payload.get("account_id")
            status = payload.get("status")
            task_id = payload.get("task_id")

            if not account_id or not status:
                raise HTTPException(status_code=400, detail="Missing required fields")

            # Verificar que el trabajo pertenezca a la cuenta
            try:
                await verify_resource_ownership(
                    db=db,
                    model=models.TrainingJob,
                    resource_id=job_id,
                    account_id=account_id,
                    error_class=ResourceNotFoundError
                )
            except ResourceNotFoundError as e:
                # Si el trabajo no existe, lanzar la excepción
                raise e
            except Exception:
                # Si el trabajo existe pero no pertenece a la cuenta, lanzar una excepción de acceso denegado
                raise HTTPException(
                    status_code=403,
                    detail="Training job does not belong to this account",
                )

            # Actualizar estado del job
            training_job = await db.get(models.TrainingJob, job_id)

            # Actualizar el estado
            training_job.status = status.lower()
            if task_id:
                training_job.task_id = task_id

            # Nota: El IDE muestra errores porque no puede verificar que estos campos sean asignables
            # Esto es normal porque SQLAlchemy utiliza descriptores especiales para las columnas
            if status.lower() == "completed":
                # Actualizar el estado y la fecha de completado
                training_job.completed_at = datetime.now(timezone.utc)

                # Actualizar la fecha del último entrenamiento exitoso en la suscripción
                # Obtener la suscripción de la cuenta
                subscription_repo = SubscriptionRepository(db, account_id=account_id)
                subscription = await subscription_repo.get_by_account(account_id)

                if subscription:
                    # Actualizar la fecha del último entrenamiento exitoso
                    subscription.last_successful_training_at = datetime.now(timezone.utc)
                    log_info(f"Updated last_successful_training_at for account {account_id}")

                # Invalidar caché de recomendaciones para esta cuenta
                await training_pipeline.invalidate_cache(account_id=account_id)
                log_info(
                    f"Training job {job_id} completed successfully for account {account_id}"
                )
            elif status.lower() == "failed":
                # Actualizar el mensaje de error y la fecha de completado
                if payload.get("error_message"):
                    training_job.error_message = str(payload.get("error_message"))
                training_job.completed_at = datetime.now(timezone.utc)
                log_error(
                    f"Training job {job_id} failed for account {account_id}: {payload.get('error_message')}"
                )

            return {
                "message": "Callback processed successfully",
                "job_id": job_id,
                "account_id": account_id,
                "status": training_job.status,
            }
    except ResourceNotFoundError:
        raise
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error in training_callback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs", response_model=List[schemas.TrainingJobStatus])
async def list_training_jobs(
    limit: int = Query(20, ge=1, le=100, description="Maximum number of jobs to return"),
    status: Optional[str] = Query(None, description="Filter by job status"),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Lista los trabajos de entrenamiento recientes para la cuenta actual.

    Este endpoint permite obtener una lista de trabajos de entrenamiento,
    ordenados por fecha de creación (más recientes primero).

    Args:
        limit: Número máximo de trabajos a devolver (máximo 100)
        status: Filtrar por estado del trabajo (opcional)
        account: Cuenta actual
        db: Sesión de base de datos

    Returns:
        Lista de trabajos de entrenamiento
    """
    try:
        from src.db.repositories.model import TrainingJobRepository
        from src.db.enums import TrainingJobStatus as JobStatus
        
        # Crear repositorio de trabajos de entrenamiento
        job_repo = TrainingJobRepository(db, account.account_id)
        
        # Obtener trabajos recientes
        if status:
            try:
                status_enum = JobStatus(status.upper())
                jobs = await job_repo.get_jobs_by_status(status_enum, limit=limit)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")
        else:
            # Obtener todos los trabajos recientes
            query = select(models.TrainingJob).where(
                models.TrainingJob.account_id == account.account_id
            ).order_by(models.TrainingJob.created_at.desc()).limit(limit)
            
            result = await db.execute(query)
            jobs = result.scalars().all()
        
        # Transformar a la respuesta esperada
        job_statuses = []
        for job in jobs:
            job_response = {
                "job_id": job.id,
                "status": job.status.value if hasattr(job.status, 'value') else str(job.status),
                "created_at": job.created_at,
                "started_at": job.started_at,
                "completed_at": job.completed_at,
                "error_message": job.error_message,
                "task_id": job.task_id,
                "parameters": job.parameters or {},
                "metrics": job.metrics or {},
            }
            
            # Si el trabajo está completado y tiene un modelo asociado, incluir información del modelo
            if (job.status == enums.TrainingJobStatus.COMPLETED and 
                hasattr(job, 'artifact_metadata_id') and job.artifact_metadata_id):
                # Buscar el modelo asociado
                model_query = select(models.ModelMetadata).where(
                    models.ModelMetadata.account_id == account.account_id,
                    models.ModelMetadata.id == job.artifact_metadata_id
                )
                model_result = await db.execute(model_query)
                model = model_result.scalar_one_or_none()

                if model:
                    job_response["model"] = {
                        "id": model.id,
                        "artifact_name": model.artifact_name,
                        "artifact_version": model.artifact_version,
                        "description": model.description,
                        "training_date": model.training_date,
                    }
            
            job_statuses.append(job_response)
        
        return job_statuses

    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error listing training jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing training jobs: {str(e)}"
        )
