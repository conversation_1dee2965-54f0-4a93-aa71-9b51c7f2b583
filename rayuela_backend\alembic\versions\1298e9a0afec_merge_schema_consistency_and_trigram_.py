"""merge schema consistency and trigram columns

Revision ID: 1298e9a0afec
Revises: 20250701_020000, 20250701_020001
Create Date: 2025-06-30 22:21:39.404242

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1298e9a0afec'
down_revision: Union[str, None] = ('20250701_020000', '20250701_020001')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
